package dataconv

import (
	"reflect"
	"testing"
	"time"

	// Original GitHub mapstructure
	original "github.com/mitchellh/mapstructure"
	// Our optimized version
	optimized "dataconv/mapstructure"
)

// Test struct definitions
type ComparisonBasic struct {
	Vstring string  `mapstructure:"vstring"`
	Vint    int     `mapstructure:"vint"`
	Vint8   int8    `mapstructure:"vint8"`
	Vint16  int16   `mapstructure:"vint16"`
	Vint32  int32   `mapstructure:"vint32"`
	Vint64  int64   `mapstructure:"vint64"`
	Vuint   uint    `mapstructure:"vuint"`
	Vbool   bool    `mapstructure:"vbool"`
	Vfloat  float64 `mapstructure:"vfloat"`
	Vextra  string  `mapstructure:"vextra"`
}

type ComparisonNested struct {
	Vfoo string          `mapstructure:"vfoo"`
	Vbar ComparisonBasic `mapstructure:"vbar"`
}

type ComparisonComplex struct {
	BasicField ComparisonBasic `mapstructure:"basic_field"`
	Nested1    struct {
		Field1  int       `mapstructure:"field1"`
		Field2  string    `mapstructure:"field2"`
		Field4  float64   `mapstructure:"field4"`
		Field5  time.Time `mapstructure:"field5"`
		Nested2 struct {
			Field1 float64 `mapstructure:"field1"`
			Field2 int     `mapstructure:"field2"`
			Field3 bool    `mapstructure:"field3"`
			Field4 string  `mapstructure:"field4"`
		} `mapstructure:"nested2"`
	} `mapstructure:"nested1"`
}

// Generate test data
func generateComparisonSimpleData() map[string]interface{} {
	return map[string]interface{}{
		"vstring": "foo",
		"vint":    42,
		"vint8":   int8(42),
		"vint16":  int16(42),
		"vint32":  int32(42),
		"vint64":  int64(42),
		"vuint":   uint(42),
		"vbool":   true,
		"vfloat":  42.42,
		"vextra":  "extra",
	}
}

func generateComparisonNestedData() map[string]interface{} {
	return map[string]interface{}{
		"vfoo": "foo",
		"vbar": map[string]interface{}{
			"vstring": "nested",
			"vint":    42,
			"vbool":   true,
			"vfloat":  3.14,
		},
	}
}

func generateComparisonComplexData() map[string]interface{} {
	return map[string]interface{}{
		"basic_field": map[string]interface{}{
			"vstring": "basic",
			"vint":    100,
			"vbool":   false,
		},
		"nested1": map[string]interface{}{
			"field1": 1,
			"field2": "nested_string",
			"field4": 1.23,
			"field5": time.Now(),
			"nested2": map[string]interface{}{
				"field1": 2.34,
				"field2": 2,
				"field3": true,
				"field4": "deep_nested",
			},
		},
	}
}

// Original mapstructure benchmarks
func BenchmarkOriginal_Simple(b *testing.B) {
	data := generateComparisonSimpleData()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result ComparisonBasic
		if err := original.Decode(data, &result); err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkOriginal_Nested(b *testing.B) {
	data := generateComparisonNestedData()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result ComparisonNested
		if err := original.Decode(data, &result); err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkOriginal_Complex(b *testing.B) {
	data := generateComparisonComplexData()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result ComparisonComplex
		if err := original.Decode(data, &result); err != nil {
			b.Fatal(err)
		}
	}
}

// Optimized mapstructure benchmarks
func BenchmarkOptimized_Simple(b *testing.B) {
	data := generateComparisonSimpleData()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result ComparisonBasic
		if err := optimized.Decode(data, &result); err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkOptimized_Nested(b *testing.B) {
	data := generateComparisonNestedData()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result ComparisonNested
		if err := optimized.Decode(data, &result); err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkOptimized_Complex(b *testing.B) {
	data := generateComparisonComplexData()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result ComparisonComplex
		if err := optimized.Decode(data, &result); err != nil {
			b.Fatal(err)
		}
	}
}

// Memory allocation comparison tests
func BenchmarkOriginal_SimpleAllocs(b *testing.B) {
	data := generateComparisonSimpleData()
	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result ComparisonBasic
		if err := original.Decode(data, &result); err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkOptimized_SimpleAllocs(b *testing.B) {
	data := generateComparisonSimpleData()
	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result ComparisonBasic
		if err := optimized.Decode(data, &result); err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkOriginal_ComplexAllocs(b *testing.B) {
	data := generateComparisonComplexData()
	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result ComparisonComplex
		if err := original.Decode(data, &result); err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkOptimized_ComplexAllocs(b *testing.B) {
	data := generateComparisonComplexData()
	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result ComparisonComplex
		if err := optimized.Decode(data, &result); err != nil {
			b.Fatal(err)
		}
	}
}

// Correctness verification test
func TestComparison_Correctness(t *testing.T) {
	// Test simple case
	data := generateComparisonSimpleData()
	var originalResult, optimizedResult ComparisonBasic
	
	err1 := original.Decode(data, &originalResult)
	err2 := optimized.Decode(data, &optimizedResult)
	
	if (err1 == nil) != (err2 == nil) {
		t.Errorf("Error status mismatch: original=%v, optimized=%v", err1, err2)
	}
	
	if err1 == nil && err2 == nil {
		if !reflect.DeepEqual(originalResult, optimizedResult) {
			t.Errorf("Results mismatch:\nOriginal: %+v\nOptimized: %+v", originalResult, optimizedResult)
		}
	}
}
