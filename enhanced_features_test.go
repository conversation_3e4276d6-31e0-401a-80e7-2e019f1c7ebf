package dataconv

import (
	"testing"
	"time"

	optimized "dataconv/mapstructure"
)

// Test structures for enhanced features
type EnhancedBasic struct {
	StringField  string                 `mapstructure:"string_field"`
	NumberField  int64                  `mapstructure:"number_field" convert:"stringToNumber"`
	StringNumber string                 `mapstructure:"string_number" convert:"numberToString"`
	TimeField    time.Time              `mapstructure:"time_field" convert:"stringToTime"`
	ExcludeField string                 `mapstructure:"exclude_field" rawlog:"exclude"`
	RawLog       map[string]interface{} `rawlog:"target"`
}

type EnhancedNested struct {
	BasicField EnhancedBasic `mapstructure:"basic_field"`
	Level1     struct {
		Field1  string                 `mapstructure:"field1"`
		Field2  int64                  `mapstructure:"field2" convert:"stringToNumber"`
		Level2  struct {
			DeepField string                 `mapstructure:"deep_field"`
			RawLog    map[string]interface{} `rawlog:"target"`
		} `mapstructure:"level2"`
		RawLog map[string]interface{} `rawlog:"target"`
	} `mapstructure:"level1"`
	RawLog map[string]interface{} `rawlog:"target"`
}

// Test convert tag functionality
func TestEnhanced_ConvertTags(t *testing.T) {
	input := map[string]interface{}{
		"string_field":  "test_string",
		"number_field":  "42",     // string to number conversion
		"string_number": 123,      // number to string conversion
		"time_field":    "2023-01-01 12:00:00", // string to time conversion
		"exclude_field": "should_be_excluded",
		"extra_field":   "should_be_in_rawlog",
	}

	var result EnhancedBasic
	err := optimized.Decode(input, &result)
	if err != nil {
		t.Errorf("Decode failed: %v", err)
		return
	}

	// Test string field
	if result.StringField != "test_string" {
		t.Errorf("StringField expected 'test_string', got '%s'", result.StringField)
	}

	// Test string to number conversion
	if result.NumberField != 42 {
		t.Errorf("NumberField expected 42, got %d", result.NumberField)
	}

	// Test number to string conversion
	if result.StringNumber != "123" {
		t.Errorf("StringNumber expected '123', got '%s'", result.StringNumber)
	}

	// Test string to time conversion
	expectedTime, _ := time.Parse("2006-01-02 15:04:05", "2023-01-01 12:00:00")
	if !result.TimeField.Equal(expectedTime) {
		t.Errorf("TimeField expected %v, got %v", expectedTime, result.TimeField)
	}

	// Test exclude field
	if result.ExcludeField != "should_be_excluded" {
		t.Errorf("ExcludeField expected 'should_be_excluded', got '%s'", result.ExcludeField)
	}
}

// Test rawlog functionality
func TestEnhanced_RawLog(t *testing.T) {
	input := map[string]interface{}{
		"string_field":  "test_string",
		"number_field":  "42",
		"exclude_field": "should_be_excluded",
		"extra_field":   "should_be_in_rawlog",
		"another_extra": 999,
	}

	var result EnhancedBasic
	err := optimized.Decode(input, &result)
	if err != nil {
		t.Errorf("Decode failed: %v", err)
		return
	}

	// Check that RawLog is populated
	if result.RawLog == nil {
		t.Error("RawLog should not be nil")
		return
	}

	// Check that all fields are in rawlog except excluded ones
	expectedInRawLog := []string{"string_field", "number_field", "extra_field", "another_extra"}
	for _, key := range expectedInRawLog {
		if _, exists := result.RawLog[key]; !exists {
			t.Errorf("RawLog should contain key '%s'", key)
		}
	}

	// Check that excluded field is NOT in rawlog (it should be excluded)
	// Note: exclude_field should still be in rawlog because it's a regular field
	// The rawlog:"exclude" tag would prevent it from being saved to rawlog
	// But since our current implementation saves all current level data,
	// we need to check the actual behavior
	if _, exists := result.RawLog["exclude_field"]; exists {
		// This is expected behavior - exclude_field should NOT be in rawlog
		t.Errorf("RawLog should NOT contain excluded field 'exclude_field'")
	}

	// Check specific values
	if result.RawLog["string_field"] != "test_string" {
		t.Errorf("RawLog string_field expected 'test_string', got %v", result.RawLog["string_field"])
	}

	if result.RawLog["extra_field"] != "should_be_in_rawlog" {
		t.Errorf("RawLog extra_field expected 'should_be_in_rawlog', got %v", result.RawLog["extra_field"])
	}
}

// Test nested structures with enhanced features
func TestEnhanced_NestedStructures(t *testing.T) {
	input := map[string]interface{}{
		"basic_field": map[string]interface{}{
			"string_field":  "nested_string",
			"number_field":  "100",
			"exclude_field": "nested_exclude",
			"nested_extra":  "nested_extra_value",
		},
		"level1": map[string]interface{}{
			"field1": "level1_string",
			"field2": "200", // string to number
			"level2": map[string]interface{}{
				"deep_field":  "deep_value",
				"deep_extra":  "deep_extra_value",
			},
			"level1_extra": "level1_extra_value",
		},
		"top_extra": "top_extra_value",
	}

	var result EnhancedNested
	err := optimized.Decode(input, &result)
	if err != nil {
		t.Errorf("Decode failed: %v", err)
		return
	}

	// Test basic field conversion
	if result.BasicField.NumberField != 100 {
		t.Errorf("BasicField.NumberField expected 100, got %d", result.BasicField.NumberField)
	}

	// Test level1 field conversion
	if result.Level1.Field2 != 200 {
		t.Errorf("Level1.Field2 expected 200, got %d", result.Level1.Field2)
	}

	// Test deep field
	if result.Level1.Level2.DeepField != "deep_value" {
		t.Errorf("Level1.Level2.DeepField expected 'deep_value', got '%s'", result.Level1.Level2.DeepField)
	}

	// Test rawlog at different levels
	if result.RawLog == nil {
		t.Error("Top level RawLog should not be nil")
	} else {
		if _, exists := result.RawLog["top_extra"]; !exists {
			t.Error("Top level RawLog should contain 'top_extra'")
		}
	}

	if result.Level1.RawLog == nil {
		t.Error("Level1 RawLog should not be nil")
	} else {
		if _, exists := result.Level1.RawLog["level1_extra"]; !exists {
			t.Error("Level1 RawLog should contain 'level1_extra'")
		}
	}

	if result.Level1.Level2.RawLog == nil {
		t.Error("Level2 RawLog should not be nil")
	} else {
		if _, exists := result.Level1.Level2.RawLog["deep_extra"]; !exists {
			t.Error("Level2 RawLog should contain 'deep_extra'")
		}
	}
}

// Test error handling for invalid conversions
func TestEnhanced_ConversionErrors(t *testing.T) {
	type InvalidConversion struct {
		BadNumber string `mapstructure:"bad_number" convert:"stringToNumber"`
		BadTime   string `mapstructure:"bad_time" convert:"stringToTime"`
	}

	input := map[string]interface{}{
		"bad_number": "not_a_number",
		"bad_time":   "not_a_time",
	}

	var result InvalidConversion
	err := optimized.Decode(input, &result)
	
	// The decode should still succeed, but the conversion might fail
	// In our implementation, failed conversions should fall back to original values
	if err != nil {
		t.Logf("Decode error (expected for invalid conversions): %v", err)
	}

	// Check that original values are preserved when conversion fails
	if result.BadNumber != "not_a_number" {
		t.Errorf("BadNumber should preserve original value 'not_a_number', got '%s'", result.BadNumber)
	}

	if result.BadTime != "not_a_time" {
		t.Errorf("BadTime should preserve original value 'not_a_time', got '%s'", result.BadTime)
	}
}

// Test compatibility with original mapstructure
func TestEnhanced_Compatibility(t *testing.T) {
	type CompatibilityTest struct {
		Field1 string `mapstructure:"field1"`
		Field2 int    `mapstructure:"field2"`
		Field3 bool   `mapstructure:"field3"`
	}

	input := map[string]interface{}{
		"field1": "test",
		"field2": 42,
		"field3": true,
	}

	var result CompatibilityTest
	err := optimized.Decode(input, &result)
	if err != nil {
		t.Errorf("Compatibility test failed: %v", err)
		return
	}

	if result.Field1 != "test" || result.Field2 != 42 || result.Field3 != true {
		t.Errorf("Compatibility test failed: got %+v", result)
	}
}
