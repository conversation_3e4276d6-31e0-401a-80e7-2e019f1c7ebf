package dataconv

import (
	"fmt"
	"log"
	"time"

	optimized "dataconv/mapstructure"
)

// Demo structures showcasing enhanced features
type User struct {
	ID          string                 `mapstructure:"id"`
	Name        string                 `mapstructure:"name"`
	Age         int64                  `mapstructure:"age" convert:"stringToNumber"`
	Score       string                 `mapstructure:"score" convert:"numberToString"`
	JoinDate    time.Time              `mapstructure:"join_date" convert:"stringToTime"`
	Password    string                 `mapstructure:"password" rawlog:"exclude"`
	Profile     Profile                `mapstructure:"profile"`
	Preferences []Preference           `mapstructure:"preferences"`
	RawLog      map[string]interface{} `rawlog:"target"`
}

type Profile struct {
	Email    string                 `mapstructure:"email"`
	Phone    string                 `mapstructure:"phone"`
	Address  Address                `mapstructure:"address"`
	Settings map[string]interface{} `mapstructure:"settings"`
	RawLog   map[string]interface{} `rawlog:"target"`
}

type Address struct {
	Street  string                 `mapstructure:"street"`
	City    string                 `mapstructure:"city"`
	Country string                 `mapstructure:"country"`
	ZipCode string                 `mapstructure:"zip_code" convert:"numberToString"`
	RawLog  map[string]interface{} `rawlog:"target"`
}

type Preference struct {
	Key     string                 `mapstructure:"key"`
	Value   string                 `mapstructure:"value"`
	Enabled bool                   `mapstructure:"enabled"`
	RawLog  map[string]interface{} `rawlog:"target"`
}

func main() {
	fmt.Println("🚀 Enhanced MapStructure Demo")
	fmt.Println("============================")

	// Sample input data with various types and extra fields
	input := map[string]interface{}{
		"id":        "user123",
		"name":      "John Doe",
		"age":       "30",                    // String to number conversion
		"score":     95,                      // Number to string conversion
		"join_date": "2023-01-15 10:30:00",  // String to time conversion
		"password":  "secret123",            // Will be excluded from rawlog
		"profile": map[string]interface{}{
			"email": "<EMAIL>",
			"phone": "555-1234",
			"address": map[string]interface{}{
				"street":   "123 Main St",
				"city":     "New York",
				"country":  "USA",
				"zip_code": 10001, // Number to string conversion
				"extra_address_info": "Near Central Park",
			},
			"settings": map[string]interface{}{
				"theme":         "dark",
				"notifications": true,
			},
			"extra_profile_info": "VIP Member",
		},
		"preferences": []interface{}{
			map[string]interface{}{
				"key":     "language",
				"value":   "en",
				"enabled": true,
				"extra_pref1": "additional_data1",
			},
			map[string]interface{}{
				"key":     "timezone",
				"value":   "UTC",
				"enabled": false,
				"extra_pref2": "additional_data2",
			},
		},
		"extra_user_info": "Premium Account",
		"metadata": map[string]interface{}{
			"source":    "api",
			"timestamp": "2023-12-01T10:00:00Z",
		},
	}

	fmt.Println("📥 Input Data:")
	printMap(input, 0)

	// Decode using enhanced mapstructure
	var user User
	err := optimized.Decode(input, &user)
	if err != nil {
		log.Fatalf("❌ Decode failed: %v", err)
	}

	fmt.Println("\n✅ Decoded Result:")
	fmt.Printf("ID: %s\n", user.ID)
	fmt.Printf("Name: %s\n", user.Name)
	fmt.Printf("Age: %d (converted from string)\n", user.Age)
	fmt.Printf("Score: %s (converted from number)\n", user.Score)
	fmt.Printf("Join Date: %s (converted from string)\n", user.JoinDate.Format("2006-01-02 15:04:05"))
	fmt.Printf("Password: %s (excluded from rawlog)\n", user.Password)

	fmt.Println("\n📧 Profile:")
	fmt.Printf("  Email: %s\n", user.Profile.Email)
	fmt.Printf("  Phone: %s\n", user.Profile.Phone)

	fmt.Println("\n🏠 Address:")
	fmt.Printf("  Street: %s\n", user.Profile.Address.Street)
	fmt.Printf("  City: %s\n", user.Profile.Address.City)
	fmt.Printf("  Country: %s\n", user.Profile.Address.Country)
	fmt.Printf("  Zip Code: %s (converted from number)\n", user.Profile.Address.ZipCode)

	fmt.Println("\n⚙️ Preferences:")
	for i, pref := range user.Preferences {
		fmt.Printf("  [%d] %s: %s (enabled: %t)\n", i, pref.Key, pref.Value, pref.Enabled)
	}

	fmt.Println("\n📋 RawLog Data (showing extra fields and original data):")
	fmt.Println("Top Level RawLog:")
	printMap(user.RawLog, 1)

	fmt.Println("\nProfile RawLog:")
	printMap(user.Profile.RawLog, 1)

	fmt.Println("\nAddress RawLog:")
	printMap(user.Profile.Address.RawLog, 1)

	fmt.Println("\nPreferences RawLog:")
	for i, pref := range user.Preferences {
		fmt.Printf("  Preference [%d] RawLog:\n", i)
		printMap(pref.RawLog, 2)
	}

	fmt.Println("\n🎯 Key Features Demonstrated:")
	fmt.Println("✓ Type conversion (string ↔ number, string → time)")
	fmt.Println("✓ RawLog functionality with flexible field naming")
	fmt.Println("✓ Exclusion of sensitive fields from rawlog")
	fmt.Println("✓ Nested structure support")
	fmt.Println("✓ Slice of structures with individual rawlogs")
	fmt.Println("✓ Preservation of extra/unknown fields")
	fmt.Println("✓ High performance with caching optimization")
}

func printMap(m map[string]interface{}, indent int) {
	if m == nil {
		fmt.Printf("%s(nil)\n", getIndent(indent))
		return
	}
	for k, v := range m {
		switch val := v.(type) {
		case map[string]interface{}:
			fmt.Printf("%s%s: (map)\n", getIndent(indent), k)
			printMap(val, indent+1)
		case []interface{}:
			fmt.Printf("%s%s: (slice with %d items)\n", getIndent(indent), k, len(val))
		case time.Time:
			fmt.Printf("%s%s: %s (time)\n", getIndent(indent), k, val.Format("2006-01-02 15:04:05"))
		default:
			fmt.Printf("%s%s: %v (%T)\n", getIndent(indent), k, v, v)
		}
	}
}

func getIndent(level int) string {
	indent := ""
	for i := 0; i < level; i++ {
		indent += "  "
	}
	return indent
}
