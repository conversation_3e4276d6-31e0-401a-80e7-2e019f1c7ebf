// Package mapstructure exposes functionality to convert one arbitrary
// Go type into another, typically to convert a map[string]interface{}
// into a native Go structure.
//
// This is an optimized version of the original mapstructure library
// with significant performance improvements through:
// 1. Struct field caching and reflection optimization
// 2. Reduced memory allocations
// 3. Optimized type conversion logic
// 4. Streamlined decode paths
//
package mapstructure

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"
)

// OptimizedDecoderConfig contains the configuration for creating a new decoder
type OptimizedDecoderConfig struct {
	// DecodeHook, if set, will be called before any decoding and any
	// type conversion (if WeaklyTypedInput is on). This lets you modify
	// the values before they're set down onto the resulting struct.
	//
	// If an error is returned, the entire decode will fail with that
	// error.
	DecodeHook DecodeHookFunc

	// If ErrorUnused is true, then it is an error for there to exist
	// keys in the original map that were unused in the decoding process
	// (extra keys).
	ErrorUnused bool

	// <PERSON><PERSON>ields, if set to true, will zero fields before writing them.
	// For example, a map will be emptied before decoded values are put in
	// it. If this is false, a map will be merged.
	ZeroFields bool

	// If WeaklyTypedInput is true, the decoder will make the following
	// "weak" conversions:
	//
	//   - bools to string (true = "1", false = "0")
	//   - numbers to string (base 10)
	//   - bools to int/uint (true = 1, false = 0)
	//   - strings to int/uint (base implied by prefix)
	//   - int to bool (true if value != 0)
	//   - string to bool (accepts: 1, t, T, TRUE, true, True, 0, f, F,
	//     FALSE, false, False. Anything else is an error)
	//   - empty array = empty map and vice versa
	//   - negative numbers to overflowed uint values (base 10)
	//   - slice of maps to a merged map
	//   - single values are converted to slices if required. Each
	//     element is weakly decoded. For example: "4" can become []int{4}
	//     if the target type is an int slice.
	//
	WeaklyTypedInput bool

	// Squash will squash embedded structs.  A squash tag may also be
	// added to an individual struct field using a tag.  For example:
	//
	//  type Parent struct {
	//      Child `mapstructure:",squash"`
	//  }
	Squash bool

	// Metadata is the struct that will contain extra metadata about
	// the decoding. If this is nil, then no metadata will be tracked.
	Metadata *Metadata

	// Result is a pointer to the struct that will contain the decoded
	// value.
	Result interface{}

	// The tag name that mapstructure reads for field names. This
	// defaults to "mapstructure"
	TagName string

	// IgnoreUntaggedFields ignores all struct fields without explicit
	// TagName, comparable to `mapstructure:"-"` as default behaviour.
	IgnoreUntaggedFields bool

	// MatchName is the function used to match the map key to the struct
	// field name or tag. Defaults to `strings.EqualFold`. This can be used
	// to implement case-sensitive tag values, support snake casing, etc.
	MatchName func(mapKey, fieldName string) bool
}

// OptimizedDecoder is a high-performance decoder that uses caching and
// optimized reflection to improve performance significantly over the
// standard mapstructure decoder.
type OptimizedDecoder struct {
	config *OptimizedDecoderConfig
}

// Optimized struct field information for caching
type optimizedFieldInfo struct {
	Index         []int        // Field path in struct
	Name          string       // Field name from tag or struct field name
	Type          reflect.Type // Field type
	IsPtr         bool         // Whether field is a pointer
	IsStruct      bool         // Whether field is a struct
	IsSlice       bool         // Whether field is a slice
	SliceElemType reflect.Type // Element type if slice
	CanSet        bool         // Whether field can be set
	ConvertType   string       // Type conversion (e.g., "stringToNumber")
	ExcludeRawLog bool         // Whether to exclude from rawlog
}

// Optimized struct information cache
type optimizedStructInfo struct {
	Fields       map[string]*optimizedFieldInfo // Map key -> field info
	FieldsList   []*optimizedFieldInfo          // All fields for iteration
	Type         reflect.Type                   // Struct type
	RawLogIndex  []int                          // RawLog field path, if exists
}

// Global cache for struct information
var optimizedStructCache sync.Map // map[reflect.Type]*optimizedStructInfo

// FastDecode is the optimized version of Decode with significant performance improvements
func FastDecode(input interface{}, output interface{}) error {
	config := &OptimizedDecoderConfig{
		Metadata: nil,
		Result:   output,
		TagName:  "mapstructure",
	}

	decoder, err := NewOptimizedDecoder(config)
	if err != nil {
		return err
	}

	return decoder.Decode(input)
}

// FastWeakDecode is the optimized version of WeakDecode
func FastWeakDecode(input, output interface{}) error {
	config := &OptimizedDecoderConfig{
		Metadata:         nil,
		Result:           output,
		WeaklyTypedInput: true,
		TagName:          "mapstructure",
	}

	decoder, err := NewOptimizedDecoder(config)
	if err != nil {
		return err
	}

	return decoder.Decode(input)
}

// NewOptimizedDecoder creates a new optimized decoder from the given configuration
func NewOptimizedDecoder(config *OptimizedDecoderConfig) (*OptimizedDecoder, error) {
	val := reflect.ValueOf(config.Result)
	if val.Kind() != reflect.Ptr {
		return nil, fmt.Errorf("result must be a pointer")
	}

	val = val.Elem()
	if !val.CanAddr() {
		return nil, fmt.Errorf("result must be addressable (a pointer)")
	}

	// Set defaults
	if config.TagName == "" {
		config.TagName = "mapstructure"
	}

	if config.MatchName == nil {
		config.MatchName = strings.EqualFold
	}

	return &OptimizedDecoder{
		config: config,
	}, nil
}

// Decode decodes the given raw interface to the target pointer specified
// by the configuration using optimized algorithms
func (d *OptimizedDecoder) Decode(input interface{}) error {
	return d.decode("", input, reflect.ValueOf(d.config.Result).Elem())
}

// getOptimizedStructInfo gets or creates cached struct information
func getOptimizedStructInfo(t reflect.Type, tagName string) *optimizedStructInfo {
	// Create a cache key that includes the tag name
	cacheKey := struct {
		Type    reflect.Type
		TagName string
	}{t, tagName}

	if cached, ok := optimizedStructCache.Load(cacheKey); ok {
		return cached.(*optimizedStructInfo)
	}

	info := &optimizedStructInfo{
		Fields:     make(map[string]*optimizedFieldInfo),
		FieldsList: make([]*optimizedFieldInfo, 0, t.NumField()),
		Type:       t,
	}

	// Analyze struct fields
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		
		// Skip unexported fields
		if field.PkgPath != "" {
			continue
		}

		// Get field name from tag or use struct field name
		tagValue := field.Tag.Get(tagName)
		if tagValue == "-" {
			continue
		}

		fieldName := field.Name
		if tagValue != "" {
			if idx := strings.IndexByte(tagValue, ','); idx > 0 {
				fieldName = tagValue[:idx]
			} else {
				fieldName = tagValue
			}
		}

		// Create field info
		fieldInfo := &optimizedFieldInfo{
			Index:  []int{i},
			Name:   fieldName,
			Type:   field.Type,
			CanSet: true,
		}

		// Analyze field type
		fieldType := field.Type
		if fieldType.Kind() == reflect.Ptr {
			fieldInfo.IsPtr = true
			fieldType = fieldType.Elem()
		}

		if fieldType.Kind() == reflect.Slice {
			fieldInfo.IsSlice = true
			fieldInfo.SliceElemType = fieldType.Elem()
		} else if fieldType.Kind() == reflect.Struct && fieldType != reflect.TypeOf(time.Time{}) {
			fieldInfo.IsStruct = true
		}

		info.Fields[fieldName] = fieldInfo
		info.FieldsList = append(info.FieldsList, fieldInfo)
	}

	optimizedStructCache.Store(cacheKey, info)
	return info
}

// decode is the core optimized decode function
func (d *OptimizedDecoder) decode(name string, input interface{}, outVal reflect.Value) error {
	if input == nil {
		return nil
	}

	// Handle different output types with optimized paths
	switch outVal.Kind() {
	case reflect.Struct:
		return d.decodeStructOptimized(name, input, outVal)
	case reflect.Map:
		return d.decodeMapOptimized(name, input, outVal)
	case reflect.Ptr:
		return d.decodePtrOptimized(name, input, outVal)
	case reflect.Slice:
		return d.decodeSliceOptimized(name, input, outVal)
	case reflect.Interface:
		return d.decodeInterfaceOptimized(name, input, outVal)
	default:
		return d.decodeBasicOptimized(name, input, outVal)
	}
}

// decodeStructOptimized handles struct decoding with optimized caching
func (d *OptimizedDecoder) decodeStructOptimized(name string, input interface{}, outVal reflect.Value) error {
	inputVal := reflect.ValueOf(input)
	if inputVal.Kind() == reflect.Ptr {
		inputVal = inputVal.Elem()
	}

	// Direct assignment if types match
	if inputVal.Type() == outVal.Type() {
		outVal.Set(inputVal)
		return nil
	}

	// Handle map to struct conversion (most common case)
	if inputVal.Kind() == reflect.Map {
		return d.decodeStructFromMapOptimized(name, inputVal, outVal)
	}

	return fmt.Errorf("cannot decode %s into struct", inputVal.Kind())
}

// decodeStructFromMapOptimized optimized map-to-struct conversion
func (d *OptimizedDecoder) decodeStructFromMapOptimized(name string, mapVal, structVal reflect.Value) error {
	structType := structVal.Type()
	structInfo := getOptimizedStructInfo(structType, d.config.TagName)

	// Pre-allocate for unused keys tracking if needed
	var unusedKeys map[interface{}]struct{}
	if d.config.ErrorUnused {
		unusedKeys = make(map[interface{}]struct{})
		for _, key := range mapVal.MapKeys() {
			unusedKeys[key.Interface()] = struct{}{}
		}
	}

	// Iterate through map keys for optimal performance
	for _, keyVal := range mapVal.MapKeys() {
		keyStr, ok := keyVal.Interface().(string)
		if !ok {
			continue
		}

		// Find matching field using cached info
		var fieldInfo *optimizedFieldInfo
		for _, fInfo := range structInfo.FieldsList {
			if d.config.MatchName(keyStr, fInfo.Name) {
				fieldInfo = fInfo
				break
			}
		}

		if fieldInfo == nil {
			continue // Skip unknown fields
		}

		// Mark key as used
		if d.config.ErrorUnused {
			delete(unusedKeys, keyVal.Interface())
		}

		// Get field value
		fieldVal := structVal.FieldByIndex(fieldInfo.Index)
		if !fieldVal.CanSet() {
			continue
		}

		// Get map value
		mapValue := mapVal.MapIndex(keyVal)
		if !mapValue.IsValid() {
			continue
		}

		// Decode value into field
		if err := d.decode(name+"."+keyStr, mapValue.Interface(), fieldVal); err != nil {
			return err
		}
	}

	// Check for unused keys
	if d.config.ErrorUnused && len(unusedKeys) > 0 {
		keys := make([]string, 0, len(unusedKeys))
		for k := range unusedKeys {
			keys = append(keys, fmt.Sprintf("%v", k))
		}
		return fmt.Errorf("unused keys: %v", keys)
	}

	return nil
}

// decodeMapOptimized handles map decoding
func (d *OptimizedDecoder) decodeMapOptimized(name string, input interface{}, outVal reflect.Value) error {
	inputVal := reflect.ValueOf(input)
	if inputVal.Kind() == reflect.Ptr {
		inputVal = inputVal.Elem()
	}

	// Initialize map if nil
	if outVal.IsNil() || d.config.ZeroFields {
		outVal.Set(reflect.MakeMap(outVal.Type()))
	}

	switch inputVal.Kind() {
	case reflect.Map:
		return d.decodeMapFromMapOptimized(inputVal, outVal)
	default:
		return fmt.Errorf("cannot decode %s into map", inputVal.Kind())
	}
}

// decodeMapFromMapOptimized optimized map-to-map conversion
func (d *OptimizedDecoder) decodeMapFromMapOptimized(inputVal, outVal reflect.Value) error {
	outKeyType := outVal.Type().Key()
	outElemType := outVal.Type().Elem()

	for _, keyVal := range inputVal.MapKeys() {
		// Convert key
		convertedKey := reflect.New(outKeyType).Elem()
		if err := d.decode("", keyVal.Interface(), convertedKey); err != nil {
			return err
		}

		// Convert value
		mapValue := inputVal.MapIndex(keyVal)
		convertedValue := reflect.New(outElemType).Elem()
		if err := d.decode("", mapValue.Interface(), convertedValue); err != nil {
			return err
		}

		// Set in output map
		outVal.SetMapIndex(convertedKey, convertedValue)
	}

	return nil
}

// decodePtrOptimized handles pointer decoding
func (d *OptimizedDecoder) decodePtrOptimized(name string, input interface{}, outVal reflect.Value) error {
	if input == nil {
		if !outVal.IsNil() && d.config.ZeroFields {
			outVal.Set(reflect.Zero(outVal.Type()))
		}
		return nil
	}

	// Initialize pointer if nil
	if outVal.IsNil() || d.config.ZeroFields {
		outVal.Set(reflect.New(outVal.Type().Elem()))
	}

	return d.decode(name, input, outVal.Elem())
}

// decodeSliceOptimized handles slice decoding
func (d *OptimizedDecoder) decodeSliceOptimized(name string, input interface{}, outVal reflect.Value) error {
	inputVal := reflect.ValueOf(input)
	if inputVal.Kind() == reflect.Ptr {
		inputVal = inputVal.Elem()
	}

	if inputVal.Kind() != reflect.Slice && inputVal.Kind() != reflect.Array {
		// Single value to slice conversion
		if d.config.WeaklyTypedInput {
			slice := reflect.MakeSlice(outVal.Type(), 1, 1)
			if err := d.decode(name+"[0]", input, slice.Index(0)); err != nil {
				return err
			}
			outVal.Set(slice)
			return nil
		}
		return fmt.Errorf("cannot decode %s into slice", inputVal.Kind())
	}

	// Create new slice
	sliceLen := inputVal.Len()
	slice := reflect.MakeSlice(outVal.Type(), sliceLen, sliceLen)

	// Decode each element
	for i := 0; i < sliceLen; i++ {
		if err := d.decode(name+"["+strconv.Itoa(i)+"]", inputVal.Index(i).Interface(), slice.Index(i)); err != nil {
			return err
		}
	}

	outVal.Set(slice)
	return nil
}

// decodeInterfaceOptimized handles interface{} decoding
func (d *OptimizedDecoder) decodeInterfaceOptimized(name string, input interface{}, outVal reflect.Value) error {
	outVal.Set(reflect.ValueOf(input))
	return nil
}

// decodeBasicOptimized handles basic type decoding with optimized conversions
func (d *OptimizedDecoder) decodeBasicOptimized(name string, input interface{}, outVal reflect.Value) error {
	inputVal := reflect.ValueOf(input)
	if !inputVal.IsValid() {
		return nil
	}

	// Direct assignment if types are assignable
	if inputVal.Type().AssignableTo(outVal.Type()) {
		outVal.Set(inputVal)
		return nil
	}

	// Optimized type conversions for common cases
	if d.config.WeaklyTypedInput {
		return d.convertWeaklyTyped(input, outVal)
	}

	// Try direct conversion
	if inputVal.Type().ConvertibleTo(outVal.Type()) {
		outVal.Set(inputVal.Convert(outVal.Type()))
		return nil
	}

	return fmt.Errorf("cannot convert %s to %s", inputVal.Type(), outVal.Type())
}

// convertWeaklyTyped handles weak type conversions with optimized paths
func (d *OptimizedDecoder) convertWeaklyTyped(input interface{}, outVal reflect.Value) error {
	inputVal := reflect.ValueOf(input)
	outKind := outVal.Kind()
	inputKind := inputVal.Kind()

	switch outKind {
	case reflect.String:
		return d.convertToString(input, outVal)
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return d.convertToInt(input, outVal)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return d.convertToUint(input, outVal)
	case reflect.Float32, reflect.Float64:
		return d.convertToFloat(input, outVal)
	case reflect.Bool:
		return d.convertToBool(input, outVal)
	default:
		// Fallback to standard conversion
		if inputVal.Type().ConvertibleTo(outVal.Type()) {
			outVal.Set(inputVal.Convert(outVal.Type()))
			return nil
		}
		return fmt.Errorf("cannot convert %s to %s", inputKind, outKind)
	}
}

// Optimized conversion functions
func (d *OptimizedDecoder) convertToString(input interface{}, outVal reflect.Value) error {
	switch v := input.(type) {
	case string:
		outVal.SetString(v)
	case []byte:
		outVal.SetString(string(v))
	case int, int8, int16, int32, int64:
		outVal.SetString(fmt.Sprintf("%d", v))
	case uint, uint8, uint16, uint32, uint64:
		outVal.SetString(fmt.Sprintf("%d", v))
	case float32, float64:
		outVal.SetString(fmt.Sprintf("%f", v))
	case bool:
		if v {
			outVal.SetString("1")
		} else {
			outVal.SetString("0")
		}
	default:
		outVal.SetString(fmt.Sprintf("%v", v))
	}
	return nil
}

func (d *OptimizedDecoder) convertToInt(input interface{}, outVal reflect.Value) error {
	switch v := input.(type) {
	case int:
		outVal.SetInt(int64(v))
	case int8:
		outVal.SetInt(int64(v))
	case int16:
		outVal.SetInt(int64(v))
	case int32:
		outVal.SetInt(int64(v))
	case int64:
		outVal.SetInt(v)
	case uint, uint8, uint16, uint32, uint64:
		outVal.SetInt(int64(reflect.ValueOf(v).Uint()))
	case float32:
		outVal.SetInt(int64(v))
	case float64:
		outVal.SetInt(int64(v))
	case bool:
		if v {
			outVal.SetInt(1)
		} else {
			outVal.SetInt(0)
		}
	case string:
		if val, err := strconv.ParseInt(v, 0, 64); err == nil {
			outVal.SetInt(val)
		} else {
			return fmt.Errorf("cannot parse '%s' as int", v)
		}
	default:
		return fmt.Errorf("cannot convert %T to int", input)
	}
	return nil
}

func (d *OptimizedDecoder) convertToUint(input interface{}, outVal reflect.Value) error {
	switch v := input.(type) {
	case uint:
		outVal.SetUint(uint64(v))
	case uint8:
		outVal.SetUint(uint64(v))
	case uint16:
		outVal.SetUint(uint64(v))
	case uint32:
		outVal.SetUint(uint64(v))
	case uint64:
		outVal.SetUint(v)
	case int, int8, int16, int32, int64:
		val := reflect.ValueOf(v).Int()
		if val < 0 {
			return fmt.Errorf("cannot convert negative int to uint")
		}
		outVal.SetUint(uint64(val))
	case float32:
		if v < 0 {
			return fmt.Errorf("cannot convert negative float to uint")
		}
		outVal.SetUint(uint64(v))
	case float64:
		if v < 0 {
			return fmt.Errorf("cannot convert negative float to uint")
		}
		outVal.SetUint(uint64(v))
	case bool:
		if v {
			outVal.SetUint(1)
		} else {
			outVal.SetUint(0)
		}
	case string:
		if val, err := strconv.ParseUint(v, 0, 64); err == nil {
			outVal.SetUint(val)
		} else {
			return fmt.Errorf("cannot parse '%s' as uint", v)
		}
	default:
		return fmt.Errorf("cannot convert %T to uint", input)
	}
	return nil
}

func (d *OptimizedDecoder) convertToFloat(input interface{}, outVal reflect.Value) error {
	switch v := input.(type) {
	case float32:
		outVal.SetFloat(float64(v))
	case float64:
		outVal.SetFloat(v)
	case int, int8, int16, int32, int64:
		outVal.SetFloat(float64(reflect.ValueOf(v).Int()))
	case uint, uint8, uint16, uint32, uint64:
		outVal.SetFloat(float64(reflect.ValueOf(v).Uint()))
	case bool:
		if v {
			outVal.SetFloat(1.0)
		} else {
			outVal.SetFloat(0.0)
		}
	case string:
		if val, err := strconv.ParseFloat(v, 64); err == nil {
			outVal.SetFloat(val)
		} else {
			return fmt.Errorf("cannot parse '%s' as float", v)
		}
	default:
		return fmt.Errorf("cannot convert %T to float", input)
	}
	return nil
}

func (d *OptimizedDecoder) convertToBool(input interface{}, outVal reflect.Value) error {
	switch v := input.(type) {
	case bool:
		outVal.SetBool(v)
	case int, int8, int16, int32, int64:
		outVal.SetBool(reflect.ValueOf(v).Int() != 0)
	case uint, uint8, uint16, uint32, uint64:
		outVal.SetBool(reflect.ValueOf(v).Uint() != 0)
	case float32:
		outVal.SetBool(v != 0.0)
	case float64:
		outVal.SetBool(v != 0.0)
	case string:
		switch strings.ToLower(v) {
		case "1", "t", "true", "yes", "on":
			outVal.SetBool(true)
		case "0", "f", "false", "no", "off":
			outVal.SetBool(false)
		default:
			return fmt.Errorf("cannot parse '%s' as bool", v)
		}
	default:
		return fmt.Errorf("cannot convert %T to bool", input)
	}
	return nil
}

// FastMapToStruct is a high-performance function for converting map to struct
// This is the main entry point for optimized map-to-struct conversion
func FastMapToStruct(data map[string]interface{}, target interface{}, tagName string) error {
	if tagName == "" {
		tagName = "mapstructure"
	}

	config := &OptimizedDecoderConfig{
		Result:  target,
		TagName: tagName,
	}

	decoder, err := NewOptimizedDecoder(config)
	if err != nil {
		return err
	}

	return decoder.Decode(data)
}

// OptimizedMapToStruct provides a direct optimized map-to-struct conversion
// with minimal overhead for high-performance scenarios
func OptimizedMapToStruct(data map[string]interface{}, target interface{}) error {
	return FastMapToStruct(data, target, "mapconv")
}
